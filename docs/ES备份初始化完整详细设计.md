## 文档概述

本文档为ES备份初始化功能的详细设计文档，基于现有的ES备份创建和绑定功能，新增ES备份查询列表、状态验证和初始化功能。

## 1. 需求分析

### 1.1 业务背景

测试人员在创建ES备份后，缺乏统一的管理界面来查看备份状态和执行初始化操作。需要提供完整的ES备份管理功能，包括备份查询、状态验证和环境初始化。

### 1.2 核心需求

1. **ES备份查询列表**: 支持按条件查询备份信息，支持分页显示
2. **备份详情查看**: 查看备份的详细信息和Jenkins执行详情
3. **备份状态验证**: 验证运行中备份的实际状态
4. **备份初始化**: 将指定备份初始化到目标环境

### 1.3 功能边界

- 基于现有ES备份功能进行扩展
- 复用现有的环境管理和业务管理接口
- 集成Jenkins流水线进行备份初始化

## 2. 系统架构设计

### 2.1 整体架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端Vue页面    │────│  Django REST API │────│  Elasticsearch  │
│                 │    │                 │    │                 │
│ - 备份查询列表   │    │ - ESBackupView  │    │ - Snapshot API  │
│ - 备份详情弹框   │    │ - ESBackupService│   │ - Status Check  │
│ - 状态验证功能   │    │ - Jenkins API   │    │                 │
│ - 初始化弹框     │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                                │
                       ┌─────────────────┐    ┌─────────────────┐
                       │   MySQL数据库    │    │  Jenkins流水线   │
                       │                 │    │                 │
                       │ - EsMgtDumpInfo │    │ - test_es_init  │
                       │ - EsMgtDumpBizBind│   │                 │
                       └─────────────────┘    └─────────────────┘
```

### 2.2 模块划分

#### 2.2.1 后端模块扩展

- **控制层**: 在现有 `ESBackupView`中新增查询列表、详情、验证、初始化接口
- **服务层**: 在现有 `ESBackupService`中新增相关业务逻辑
- **Jenkins集成**: 新增Jenkins流水线触发接口

#### 2.2.2 前端模块扩展

- **标签页扩展**: 在现有ES备份管理页面中新增"ES初始化"标签页
- **弹框组件**: 详情弹框、初始化弹框
- **API扩展**: 新增相关API接口封装

### 2.3 页面结构说明

基于原型图，前端页面结构如下：

- **主页面**: ES备份管理页面包含三个标签页
  - ES初始化（新增）
  - ES备份创建（现有）
  - ES建交与归档（现有）
- **ES初始化标签页**: 包含查询条件、列表展示、操作按钮
- **详情弹窗**: 显示备份文件的业务绑定详情
- **初始化弹窗**: 选择业务、分支、环境进行初始化
- **进度展示**: 8步骤流程进度条

## 3. 数据库设计

### 3.1 现有表结构复用

基于现有的 `EsMgtDumpInfo`和 `EsMgtDumpBizBind`表，无需新增表结构。

### 3.2 状态字段扩展

在现有状态基础上，可能需要新增状态：

- `RUNNING`: 运行中（对应ES的IN_PROGRESS状态）
- `INITIALIZING`: 初始化中

## 4. 后端详细设计（spider）

### 4.1 API接口设计

#### 4.1.1 ES初始化列表查询接口

- **接口定位**：服务于“ES初始化”页签的查询表单，与前端文件名、备份说明、备份所属 ES 三个查询条件一一对应，支撑分页展示列表。
- **请求方式与路径**：使用 GET 方法，路径 `/spider/es_mgt/backups/init-list`，与现有备份模块保持统一前缀，便于前端统一封装。
- **请求参数**：

| 名称        | 字段名                | 类型   | 是否必填 | 说明                                                   |
| ----------- | --------------------- | ------ | -------- | ------------------------------------------------------ |
| 文件名      | es_dump_name          | string | 否       | 来源于“文件名”输入框，支持模糊匹配，留空时不参与过滤 |
| 备份说明    | description           | string | 否       | 来源于“备份说明”输入框，支持模糊匹配                 |
| 备份所属 ES | source_es_module_name | string | 否       | 来源于“备份所属 ES”下拉框，采用精确匹配              |
| 分页页码    | page                  | int    | 是       | 分页页码，默认从 1 起始，与分页组件联动                |
| 每页条数    | page_size             | int    | 是       | 分页大小，默认 10，允许前端调整                        |

- **查询规则**：
  - 过滤条件同时支持组合过滤，若全部为空则返回近期数据，按创建时间倒序排列，确保新备份优先展示。
  - 模糊匹配使用数据库统一的 `LIKE` 语义，避免前后端字符串处理差异。
  - 仅返回前端展示所需字段，避免过量数据传输，如需详情将在详情接口单独获取。
- **返回结构**：

| 名称                  | 字段名                       | 类型   | 是否必含 | 说明                                                 |
| --------------------- | ---------------------------- | ------ | -------- | ---------------------------------------------------- |
| 总条数                | total                        | int    | 是       | 查询结果总数，支撑分页控件展示                       |
| 当前页码              | page                         | int    | 是       | 当前页码，回显分页状态                               |
| 当前页大小            | page_size                    | int    | 是       | 当前分页大小，回显分页状态                           |
| ES 初始化记录列表开始 | records                      | List   | 是       | 列表数据集，元素为备份记录对象                       |
| 备份文件名            | records[].esDumpName         | string | 是       | 展示列“es备份文件名”                               |
| 备份说明              | records[].description        | string | 是       | 展示列“备份说明”                                   |
| 索引数                | records[].indexCount         | int    | 是       | 展示列“索引数”                                     |
| 备份状态              | records[].status             | string | 是       | 展示列“备份状态”，枚举与现网保持一致               |
| 备份所属 ES           | records[].sourceEsModuleName | string | 是       | 展示列“备份所属 ES”                                |
| 创建人                | records[].creator            | string | 是       | 展示列“创建人”                                     |
| 最后验证时间          | records[].lastVerifyTime     | string | 否       | 用于展示最近一次状态验证时间，无数据时前端展示“--” |
| 最近初始化构建号      | records[].lastInitBuildNo    | string | 否       | 初始化流水线的最近一次构建号，支持跳转 Jenkins       |
| 允许初始化标记        | records[].initAllowed        | bool   | 是       | 控制“初始化”按钮的可操作状态                       |
| ES 初始化记录列表结束 | records                      | List   | 是       | 标记记录数组结构结束                                 |

- **后端实现要点**：
  - 控制层在现有 `ESBackupView` 中新增对应方法，调用服务层统一的查询能力；方法中需补充参数合法性校验与默认值设定。
  - 服务层（`ESBackupService`）复用既有 `EsMgtDumpInfo` 实体，增加列表查询专用的分页方法，确保索引命中和查询性能；若状态字段需扩展枚举映射，务必在此处完成转换。
  - 将最近一次验证与初始化信息从历史记录中提取（若存在，则取最新一次），并组合到返回数据，便于前端展示。
  - 当数据条目超过单页时，维持总条数统计一致性，避免前端翻页后出现总数跳变。
- **异常与安全**：
  - 请求必须在登录态下调用，沿用现有权限框架校验 `ES管理` 权限；无权限时返回业务错误并提示联系管理员。
  - 若查询条件不合法（如页码 < 1、页大小超出项目约定范围），控制层需返回参数错误提示，不进入数据库查询。
  - 查询失败或数据库异常时，记录详细日志（含查询条件和调用人），响应中返回统一错误文案，提示稍后重试。
- **与前端协作**：
  - 前端在 `spider-api/es-backup.js` 中新增查询方法，命名与现有备份查询保持风格一致；表单重置时需同步重置分页参数，避免携带无效条件。
  - 页签切换至“ES初始化”时自动触发一次查询，填充默认列表；用户点击分页组件或查询按钮时再次调用该接口。
  - 返回的索引状态、初始化标记用于控制“验证”“初始化”按钮的禁用态，与原型交互保持一致。

#### 4.1.2 备份详情查询接口

- **接口定位**：支撑“ES初始化”页签列表中“详情”按钮的弹框展示，用于获取指定备份文件的元信息及业务绑定的初始化执行记录。
- **请求方式与路径**：使用 GET 方法，路径 `/spider/es_mgt/backups/{es_dump_name}/init-detail`，路径参数为备份文件名。
- **请求参数**：

| 名称       | 字段名       | 类型   | 是否必填 | 说明                                                        |
| ---------- | ------------ | ------ | -------- | ----------------------------------------------------------- |
| 备份文件名 | es_dump_name | string | 是       | 路径参数，取自列表中当前行 `esDumpName`，决定后端查询目标 |

- **返回结构**：

| 名称                   | 字段名                             | 类型   | 是否必含 | 说明                                                          |
| ---------------------- | ---------------------------------- | ------ | -------- | ------------------------------------------------------------- |
| 备份文件名             | esDumpName                         | string | 是       | 弹框标题展示的备份文件名                                      |
| 备份说明               | description                        | string | 是       | 备份备注信息，用于弹框顶部信息区域                            |
| 备份所属 ES            | sourceEsModuleName                 | string | 是       | 备份所属 ES 模块名称                                          |
| 创建人                 | creator                            | string | 是       | 备份创建人                                                    |
| 创建时间               | createTime                         | string | 是       | 备份创建时间，格式 `yyyyMMddHHmmss`                         |
| 最近验证状态           | lastVerifyStatus                   | string | 否       | 备份最近一次验证的结果，供弹框补充展示                        |
| 最近验证时间           | lastVerifyTime                     | string | 否       | 最近一次验证的时间，无数据时返回空字符串                      |
| 业务初始化记录列表开始 | bizInitRecords                     | List   | 是       | 与原型表格对应的业务级初始化记录数组                          |
| 业务名称               | bizInitRecords[].bizName           | string | 是       | 表格列“业务”                                                |
| 分支                   | bizInitRecords[].bizBranch         | string | 是       | 表格列“分支”，通常为 `dev/master` 等                      |
| 最近一次初始化环境     | bizInitRecords[].lastInitSuiteCode | string | 否       | 表格列“最近一次初始化环境”，无历史时返回空字符串            |
| 最近一次初始化时间     | bizInitRecords[].lastInitTime      | string | 否       | 表格列“最近一次初始化时间”，格式 `yyyyMMddHHmmss`         |
| 操作详情跳转链接       | bizInitRecords[].detailUrl         | string | 否       | “详情”按钮的 Jenkins 或任务详情跳转地址，无历史则为空字符串 |
| 业务初始化记录列表结束 | bizInitRecords                     | List   | 是       | 标记记录数组结构结束                                          |

- **后端实现要点**：
  - 控制层校验 `es_dump_name` 存在性，结合当前登录用户权限决定是否返回详情数据。
  - 服务层需查询 `EsMgtDumpInfo` 及其业务绑定记录 `EsMgtDumpBizBind`，同时从初始化历史表或日志表中提取最近一次构建号、环境、时间信息，组合成 `bizInitRecords`。
  - 若某业务从未执行初始化，关联记录仍需返回，初始化环境与时间字段留空，前端按“--”展示。
  - `detailUrl` 由后端根据 Jenkins 构建号或内部日志页生成，确保前端无需拼接。
  - 数据未命中时返回业务错误码与提示信息，前端展示“记录不存在，请检查备份文件名”。
- **与前端协作**：
  - 弹框打开时调用该接口并使用返回数据渲染顶部信息与业务表格。
  - “详情”按钮点击后直接跳转 `detailUrl`，当链接为空时按钮需置灰并提示“暂无详情”。
  - 若后端返回权限错误，前端需提示用户联系管理员开通权限，并关闭弹框避免展示空白内容。

#### 4.1.3 备份状态验证接口

- **接口定位**：供“ES初始化”列表中“验证”按钮触发，用于对处于运行中 (`RUNNING`) 的备份执行文件存在校验与索引数比对，并回写最新状态。
- **请求方式与路径**：使用 POST 方法，路径 `/spider/es_mgt/backups/status/verify`，保持与现网状态接口前缀一致，便于权限复用。
- **请求参数**：

| 名称       | 字段名       | 类型   | 是否必填 | 说明                                            |
| ---------- | ------------ | ------ | -------- | ----------------------------------------------- |
| 备份文件名 | es_dump_name | string | 是       | 取自列表当前行 `esDumpName`，需验证的备份标识 |
| 操作人     | operator     | string | 是       | 当前登录用户名，用于审计记录                    |

- **返回结构**：

| 名称             | 字段名                    | 类型   | 是否必含 | 说明                                                                                |
| ---------------- | ------------------------- | ------ | -------- | ----------------------------------------------------------------------------------- |
| 校验结果         | verifyResult              | string | 是       | 校验后的最终状态，取值 `SUCCESS`、`RUNNING`、`FAILED`，与前端状态展示保持一致 |
| 提示信息         | message                   | string | 是       | 面向用户的提示文案，说明本次验证的结果或异常原因                                    |
| 实际索引数       | actualIndexCount          | int    | 否       | ES 实际返回的索引数，用于前端辅助展示，无数据时返回 0                               |
| 预期索引数       | expectedIndexCount        | int    | 否       | 备份记录中的基准索引数，用于对比展示                                                |
| 校验时间         | verifyTime                | string | 是       | 本次验证完成时间，格式 `yyyyMMddHHmmss`                                           |
| 校验步骤明细开始 | verifySteps               | List   | 是       | 按顺序输出验证的各步骤结果                                                          |
| 步骤名称         | verifySteps[].stepName    | string | 是       | 例如“文件存在校验”“索引数比对”                                                  |
| 步骤结果         | verifySteps[].stepStatus  | string | 是       | 取值 `SUCCESS`、`FAILED`，用于前端展示每个步骤的状态                            |
| 详情说明         | verifySteps[].stepMessage | string | 否       | 步骤执行的详细说明或失败原因                                                        |
| 校验步骤明细结束 | verifySteps               | List   | 是       | 标记步骤数组结构结束                                                                |

- **后端实现要点**：
  - 控制层需校验备份当前状态，仅当状态为 `RUNNING` 或 `INITIALIZING` 时允许触发，其他状态直接返回提示“无需验证”。
  - 服务层执行两项校验：对象存储中备份文件是否存在、ES 集群中索引数是否与基准值相同；索引数大于基准返回 `FAILED`，等于则返回 `SUCCESS`，小于时保持 `RUNNING` 并提示等待。
  - 校验结果需落库更新 `EsMgtDumpInfo` 的状态、`last_verify_time`、`last_verify_result` 字段，同时追加一条校验历史记录便于审计。
  - 异常场景（如 ES 接口调用失败）需捕获并返回 `FAILED`，`message` 中写明异常类型，便于前端提示用户重试。
- **与前端协作**：
  - “验证”按钮点击后调用该接口，执行过程中展示 loading 状态；接口成功后刷新列表以获取最新状态。
  - 当返回 `verifySteps` 时，前端在消息提示或详情弹框中展示每个步骤的结果，帮助用户定位失败环节。
  - 若接口返回权限错误或参数错误，前端需提示用户联系管理员或检查备份状态，并保持当前弹框/列表不跳转。

#### 4.1.4 备份初始化执行接口

- **接口定位**：支撑“ES初始化”弹框中“确定”按钮的初始化操作，触发 Jenkins `test_es_init` 流水线，并记录构建信息用于后续跟踪。
- **请求方式与路径**：使用 POST 方法，路径 `/spider/es_mgt/backups/init-execute`。
- **请求参数**：

| 名称         | 字段名            | 类型   | 是否必填 | 说明                                                           |
| ------------ | ----------------- | ------ | -------- | -------------------------------------------------------------- |
| 备份文件名   | es_dump_name      | string | 是       | 弹框标题中当前备份文件名，对应列表 `esDumpName`              |
| 业务编码     | biz_code          | string | 是       | 下拉框选中的业务标识，需与现有业务接口返回值一致               |
| 业务名称     | biz_name          | string | 是       | 业务中文名称，便于后端记录审计信息                             |
| 业务分支     | biz_branch        | string | 是       | 分支字段，如 `dev`、`master`，来源于业务分支下拉框         |
| 目标环境编码 | target_suite_code | string | 是       | 环境下拉框选择结果，如 `it101`，需通过现有环境接口校验合法性 |
| 强制清理标记 | force_clean       | bool   | 否       | 是否在初始化前强制清理目标环境，默认 false                     |
| 操作人       | operator          | string | 是       | 当前登录用户名，用于审计与 Jenkins 入参                        |

- **返回结构**：

| 名称             | 字段名                      | 类型   | 是否必含 | 说明                                                         |
| ---------------- | --------------------------- | ------ | -------- | ------------------------------------------------------------ |
| 初始化任务状态   | initStatus                  | string | 是       | 返回 `TRIGGERED` 表示触发成功，`REJECTED` 表示校验未通过 |
| Jenkins 构建号   | buildNumber                 | string | 否       | 触发成功时返回流水线构建号，用于后续查询                     |
| Jenkins 访问地址 | buildUrl                    | string | 否       | 触发成功时返回构建详情页地址，供前端跳转                     |
| 提示信息         | message                     | string | 是       | 前端提示文案，包含成功或失败原因                             |
| 入参回显开始     | requestEcho                 | Object | 否       | 触发成功时返回本次入参摘要                                   |
| 备份文件名       | requestEcho.esDumpName      | string | 是       | 入参回显字段                                                 |
| 业务编码         | requestEcho.bizCode         | string | 是       | 入参回显字段                                                 |
| 业务分支         | requestEcho.bizBranch       | string | 是       | 入参回显字段                                                 |
| 目标环境编码     | requestEcho.targetSuiteCode | string | 是       | 入参回显字段                                                 |
| 强制清理标记     | requestEcho.forceClean      | bool   | 是       | 入参回显字段                                                 |
| 入参回显结束     | requestEcho                 | Object | 否       | 标记回显对象结束                                             |

- **后端实现要点**：
  - 控制层需校验备份状态必须为 `SUCCESS`，同时校验 `initAllowed` 为 true，避免在不可初始化状态下触发。
  - 服务层验证业务、分支、环境组合合法性，并校验目标环境当前是否已有执行中的初始化任务，若存在则返回 `REJECTED`。
  - 合法请求需按项目约定封装 Jenkins 入参并触发 `test_es_init` 流水线，触发成功后记录构建号、参数、操作人、开始时间。
  - 若触发 Jenkins 失败（接口异常、参数错误），需捕获异常并返回 `REJECTED`，`message` 中写明原因，同时落库失败日志以便排查。
  - 初始化任务创建后需将 `EsMgtDumpInfo` 状态更新为 `INITIALIZING`，并写入 `last_init_build` 字段，供列表与详情展示。
- **与前端协作**：
  - 弹框点击“确定”后调用该接口，请求前需完成必填项校验；调用过程展示 loading，成功后关闭弹框并提示构建号。
  - 当返回 `buildUrl` 时，前端在成功提示中提供“查看流水线”跳转；当状态为 `REJECTED` 时，保持弹框开启并展示 `message`。
  - 触发成功后，前端需刷新列表或详情以展示最新状态，并可轮询构建进度接口（后续章节定义）。

### 4.2 Jenkins 流水线集成

#### 4.2.1 流水线基础信息

- **流水线名称**：`test_es_init`
- **触发方式**：通过后端接口调用 Jenkins REST API，传入备份、业务、环境等参数。
- **并发策略**：同一备份与同一目标环境组合在运行态时拒绝再次触发；不同组合可并行执行，避免资源争用。

#### 4.2.2 参数定义

| 名称         | 字段名            | 类型    | 是否必填 | 说明                                              |
| ------------ | ----------------- | ------- | -------- | ------------------------------------------------- |
| 备份文件名   | ES_DUMP_NAME      | string  | 是       | 对应后端参数 `es_dump_name`，定位初始化目标备份 |
| 业务编码     | BIZ_CODE          | string  | 是       | 供初始化脚本选择业务配置                          |
| 业务名称     | BIZ_NAME          | string  | 是       | 写入 Jenkins 日志，便于人读识别                   |
| 业务分支     | BIZ_BRANCH        | string  | 是       | 指定业务分支（如 `dev`）                        |
| 目标环境编码 | TARGET_SUITE_CODE | string  | 是       | 指定目标环境（如 `it101`），脚本依据此连接集群  |
| 强制清理标记 | FORCE_CLEAN       | boolean | 否       | 是否在恢复前执行清理，默认为 false                |
| 触发人       | OPERATOR          | string  | 是       | 操作人信息，写入日志与通知                        |
| 构建唯一标识 | BUILD_TOKEN       | string  | 是       | 后端生成的唯一 ID，用于回调与幂等控制             |

#### 4.2.3 流程步骤设计

| 顺序 | 阶段名称        | 目标                 | 关键动作                                        |
| ---- | --------------- | -------------------- | ----------------------------------------------- |
| 1    | Start → 初始化 | 构建环境准备         | 打印参数，校验入参合法性                        |
| 2    | 验证 ES 服务    | 确认目标环境可访问   | 调用脚本测试 ES 集群健康状态                    |
| 3    | 验证 ES 备份    | 校验备份文件有效性   | 校验对象存储中备份存在，解压校验索引列表        |
| 4    | ES 数据清空     | 清理目标环境历史数据 | 根据 `FORCE_CLEAN` 标记选择执行全量清空或跳过 |
| 5    | ES 备份恢复     | 执行数据恢复         | 运行恢复脚本，将快照恢复到目标环境              |
| 6    | ES 脚本执行     | 扩展任务             | 针对业务执行补充脚本，如索引别名处理            |
| 7    | 验证结果        | 结果校验             | 比对索引数、文档数，确保恢复成功                |
| 8    | End             | 构建收尾             | 输出总结，回传构建结果                          |

- 各阶段需输出结构化日志 `STEP={序号} STATUS={状态} MSG={说明}`，便于后端与前端解析。

#### 4.2.4 成功与失败标准

- **成功**：所有阶段状态 `SUCCESS`，脚本在结束阶段写入恢复完成信息。后端接收回调后将备份状态更新为 `SUCCESS`，记录 `last_init_time`、`last_init_build`。
- **失败**：任一阶段失败即终止流水线，并返回失败原因。后端在回调中将状态标记为 `FAILED`，记录失败阶段与详情，供前端提示。
- **超时**：整体超时设定 2 小时，关键阶段（如恢复、验证）各设 30 分钟。超时视为失败，回调结果标记 `TIMEOUT`。

#### 4.2.5 回调与状态同步

- Jenkins 在构建结束时调用后端回调接口（后续章节定义），参数包含 `BUILD_TOKEN`、`buildNumber`、`result`、`logUrl`、`stepSummary` 等。
- 后端依据 `BUILD_TOKEN` 定位任务，更新 `EsMgtDumpInfo` 状态与初始化历史表，并写入日志链接，供前端详情展示。
- 若回调长时间未到达，后端定时任务需轮询 Jenkins API 获取构建状态，防止状态悬挂。

#### 4.2.6 监控与告警

- 流水线失败、超时需触发企业微信/邮件告警，内容包含操作人、备份文件名、目标环境、失败阶段、失败原因。
- Jenkins 作业需开启失败重试（1 次）与失败通知，确保异常及时发现。
- 后端保存构建历史数据 90 天，支持后续问题回溯与统计分析。
