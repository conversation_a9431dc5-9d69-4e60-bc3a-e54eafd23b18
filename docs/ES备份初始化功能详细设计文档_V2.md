# ES备份初始化详细设计（V2.1）

## 评审结论
- `ES备份初始化功能详细设计文档.md:265-296` 自定义了 `query`、`detail`、`verify`、`initialize` 等新接口，但现网前端已通过 `deploy_website/src/spider-api/es-backup.js:3-45` 复用 `/spider/es_mgt/backups/list`、`/status/` 等接口，文档未说明如何兼容或替换旧接口，存在对实际项目接口的遗漏。
- `ES备份初始化功能详细设计文档.md:438-540` 计划调用 `getEnvList` 获取环境信息，而项目当前在同模块通过 `getEnvModules` 复用 ES 备份环境/模块映射，未评估直接替换后的数据结构差异，可能导致初始化表单缺少模块信息。
- `ES备份初始化功能详细设计文档.md:150-160` 对“验证”接口仅传入 `es_dump_name`，未覆盖需求中的“文件存在校验、索引数对比并更新状态”细则，也未说明索引基准值来源与存储，导致关键校验逻辑缺失。

## ES备份初始化功能详细设计

### 1. 文档概述
- 功能名称：ES备份初始化
- 文档版本：V2.1
- 编写时间：2025-09-23 14:39:34
- 适用角色：前端、后端、测试、运维、产品
- 参考资料：《ES备份初始化需求》《现有ES备份创建/绑定实现》

### 2. 背景与目标
- 现状：备份可创建与绑定，但缺少集中查询入口、运行中备份的自动校验、以及对指定业务/环境的初始化能力。
- 目标：基于既有备份管理模块，补齐“查询—详情—验证—初始化”闭环，减少测试人员手工核对与脚本操作成本。

### 3. 需求范围
- 功能：备份列表查询（分页、条件过滤）、详情弹框（含 Jenkins 执行信息）、运行中备份验证、备份初始化触发 Jenkins `test_es_init`。
- 数据来源：复用 `EsMgtDumpInfo`、`EsMgtDumpBizBind` 表与对象存储中的备份文件。
- 外部依赖：Jenkins 流水线、数据开发环境列表接口、业务/分支查询接口。
- 非功能：接口响应≤3s，前端页面保持单文件长度<1000行，错误提示明确可理解。

### 4. 业务流程
1. 用户进入“ES备份管理”，在原“ES备份创建”页签左侧新增并默认选中“ES初始化”页签。
2. 用户在查询区填写文件名、备份说明、备份所属 ES，再触发查询获得分页列表。
3. 列表提供“详情”“验证”“初始化”操作：
   - 详情：展示备份元信息、关联业务、最近一次 Jenkins 构建、错误日志等。
   - 验证：仅对状态为“运行中”的备份可点，后端执行文件存在与索引对比后回写状态。
   - 初始化：选择业务、分支、目标环境，调用 Jenkins 流水线并在页面显示触发结果。
4. 初始化完成后，用户可刷新或等待轮询同步 Jenkins 结果与备份状态。

### 5. 模块划分
- 前端：
  - 调整 `es-backup-mgt.vue` 页签顺序，将“ES初始化”插入最左侧并设为默认选中。
  - 新增列表组件 `es-backup/es-backup-init.vue`（承载查询表单、表格、分页）。
  - 新增弹框组件 `es-backup/backup-detail-modal.vue`、`es-backup/backup-init-modal.vue`，各自拆分逻辑确保文件长度受控。
  - 在 `spider-api/es-backup.js` 新增初始化相关请求方法，复用既有环境与业务接口。
- 后端：
  - 在现有 ES 备份控制层扩展列表查询、详情、验证、初始化接口，保持 `/spider/es_mgt/backups` 前缀，参数与前端一致。
  - 服务层补充验证逻辑、初始化事务处理、状态回写。
  - Jenkins 服务封装 `test_es_init` 触发与结果查询。
- 脚本与任务：复用或补充现有脚本完成备份恢复与索引校验。

### 6. 前端设计要点
- 页签布局：`es-backup-mgt.vue` 中的 `Tabs` 顺序从左到右为“ES初始化”“ES备份创建”“ES提交与归档”，默认聚焦“ES初始化”，其余逻辑保持懒加载。
- 查询表单：字段包含文件名（模糊匹配）、备份说明、备份所属 ES，输入框与下拉框如原型所示横向排列，右侧提供查询与重置按钮。
- 列表展示：列包含文件名、说明、索引数、备份状态、备份所属 ES、创建人、操作，行高与分页样式遵循现有备份模块规范。操作列禁用逻辑：状态≠RUNNING 时禁用“验证”，状态≠SUCCESS 时禁用“初始化”。
- 详情弹框：展示备份元数据（环境、模块、索引、文档数、文件大小、创建/更新时间、备注）、最近三次 Jenkins 构建概览和错误日志摘要。弹框通过详情接口一次性获取数据并提供跳转 Jenkins 日志的链接文案。
- 初始化弹框：表单项包含业务、业务分支、目标环境、可选强制清理标记及操作确认。业务/分支数据复用 `getBisNameLIstInfo` 与 `get_test_iter_list`，环境下拉继续调用 `getEnvModules` 并展示“环境编码-容器节点”格式。提交前校验必填项并提示初始化条件。
- 动效与提示：操作期间展示 Spin 组件；接口异常通过统一 Message 输出并附带人工处理建议；初始化成功时提示 Jenkins 构建号并可直接跳转。
- 文件拆分：列表组件负责状态管理和接口调用；两个弹框组件只管理展示与表单，确保单文件长度控制在 600 行以内。

### 7. 后端接口设计
- 列表查询：提供 GET `/spider/es_mgt/backups/init-list`，支持文件名、备份说明、备份所属 ES、页码、分页大小参数，返回总数、数据列表、备份状态、索引数、最近验证时间、最新 Jenkins 构建号。
- 详情查询：提供 GET `/spider/es_mgt/backups/{es_dump_name}/init-detail`，返回备份元信息、关联业务、验证历史、最近三次 Jenkins 构建信息、错误日志。
- 验证执行：提供 POST `/spider/es_mgt/backups/status/verify`，请求体包含 `es_dump_name`、`operator`，执行文件存在校验、索引对比（大于初始→FAIL，小于初始→RUNNING，等于初始→SUCCESS），结果需回写数据库并返回状态与提示信息。
- 初始化触发：提供 POST `/spider/es_mgt/backups/init-execute`，请求体包含 `es_dump_name`、`biz_code`、`biz_test_iter_br`、`target_suite_code`、`operator`、强制清理标记，服务端校验业务与环境并记录任务后触发 Jenkins。
- Jenkins 结果查询：提供 GET `/spider/es_mgt/backups/init-execute/{build_number}`，用于页面刷新初始化进度。
- 权限校验：所有接口需验证登录态；初始化操作额外校验 ES 管理角色权限。

### 8. Jenkins 集成方案
- 流水线名称为 `test_es_init`，入参包含备份名称、业务编码、业务分支、目标环境、触发人、强制清理标记。
- 流程步骤：Start → 初始化 → 验证ES服务 → 验证ES备份 → ES数据清空 → ES备份恢复 → ES脚本执行 → 验证结果 → End，各步骤输出结构化日志 `STEP={编号} STATUS={结果}` 以便前端展示。
- 超时与重试：整体超时 2 小时，关键步骤超时 30 分钟，失败重试 1 次。
- 构建记录：触发时入库记载构建号、参数、开始时间；Jenkins 结束后通过回调更新结果、日志链接与结束时间。
- 异常兜底：触发失败或网络异常需立即返回错误信息并保存失败记录，支持详情页提示与人工排查。

### 9. 数据与状态管理
- 数据表：复用 `EsMgtDumpInfo` 与 `EsMgtDumpBizBind`，新增字段 `last_verify_time`、`last_verify_result`、`last_init_build`、`last_init_time`、`init_allowed` 记录初始化能力。
- 状态枚举：`RUNNING`、`SUCCESS`、`FAILED`、`INITIALIZING`，需与前端展示一致。
- 索引基准：备份创建完成后保存初始索引数；验证流程必须读取该值进行对比，缺失时阻断初始化并提示补录。

### 10. 权限与审计
- 权限：仅 ES 管理权限用户可见并访问“ES初始化”页签；初始化操作记录触发人、目标环境、构建号。
- 审计：验证与初始化的每次执行结果需持久化，支持后续审核与回溯。

### 11. 异常处理与用户反馈
- 查询失败：提示“查询失败，请稍后重试”，同时记录 errorId 以便排查。
- 验证失败：返回明确原因（文件缺失、索引不匹配、接口异常），保持原状态支持再次验证。
- 初始化失败：提示 Jenkins 返回的失败原因，保留失败记录并在详情弹框展示日志链接。
- 回调超时：后台定时任务轮询 `INITIALIZING` 记录，超时后自动标记为失败并通知页面刷新。

### 12. 测试与上线策略
- 重点测试：多条件查询、页签顺序与默认选中、详情字段准确性、验证状态三种分支、初始化成功与失败场景、重复触发校验、权限隔离、Jenkins 超时、回调丢失。
- 回归范围：确保“ES备份创建”“ES提交与归档”原功能不受影响，尤其是页签切换和接口复用部分。
- 上线流程：后端灰度→验证 Jenkins 回调→前端发布→冒烟→开启监控并更新操作手册。
